import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
// MainLayout removed - handled by routing system
import { RTLText, RTLFlex } from '../../components/common';
import { useLanguage } from '../../hooks/useLanguage';
import {
  BookOpen,
  Search,
  Filter,
  Grid,
  List,
  Star,
  Clock,
  Users,
  Download,
  Eye,
  Heart,
  TrendingUp,
  Zap,
  Crown,
  Tag,
  Calendar,
  BarChart3
} from 'lucide-react';

interface Template {
  id: number;
  name: string;
  description: string;
  category: string;
  type: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: number;
  sections: number;
  popularity: number;
  downloads: number;
  rating: number;
  isNew?: boolean;
  isPremium?: boolean;
  isFavorite?: boolean;
  author: string;
  createdAt: string;
  tags: string[];
  previewImage?: string;
}

const TemplateLibraryPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  const [templates, setTemplates] = useState<Template[]>([]);
  const [categories, setCategories] = useState<Array<{id: string, name: string, count: number}>>([]);
  const [userFavorites, setUserFavorites] = useState<number[]>([]);
  const [loading, setLoading] = useState(true);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  const [sortBy, setSortBy] = useState('popularity');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);

  const sortOptions = [
    { id: 'popularity', name: t('templates.sort.popularity') },
    { id: 'newest', name: t('templates.sort.newest') },
    { id: 'rating', name: t('templates.sort.rating') },
    { id: 'downloads', name: t('templates.sort.downloads') },
    { id: 'name', name: t('templates.sort.name') }
  ];

  useEffect(() => {
    loadTemplates();
    loadCategories();
    loadUserFavorites();
  }, [selectedCategory, selectedDifficulty, sortBy]);

  const loadCategories = async () => {
    setCategoriesLoading(true);
    try {
      const { businessPlanTemplatesAPI } = await import('../../services/templateCustomizationApi');
      const categoriesData = await businessPlanTemplatesAPI.getCategories();

      // Transform categories data and add "all" option
      const transformedCategories = [
        {
          id: 'all',
          name: t('templates.categories.all', 'All Categories'),
          count: categoriesData.reduce((sum: number, cat: any) => sum + cat.count, 0)
        },
        ...categoriesData.map((cat: any) => ({
          id: cat.name.toLowerCase().replace(/\s+/g, '_'),
          name: cat.name,
          count: cat.count
        }))
      ];

      setCategories(transformedCategories);
    } catch (error) {
      console.error('Error loading categories:', error);
      // Set default categories on error
      setCategories([
        { id: 'all', name: t('templates.categories.all', 'All Categories'), count: 0 }
      ]);
    } finally {
      setCategoriesLoading(false);
    }
  };

  const loadUserFavorites = async () => {
    try {
      const { businessPlanTemplatesAPI } = await import('../../services/templateCustomizationApi');
      const favorites = await businessPlanTemplatesAPI.getUserFavorites();
      setUserFavorites(favorites);
    } catch (error) {
      console.error('Error loading user favorites:', error);
      // Silently fail for favorites - not critical functionality
      setUserFavorites([]);
    }
  };

  const loadTemplates = async () => {
    setLoading(true);
    setError(null);
    try {
      const { businessPlanTemplatesAPI } = await import('../../services/templateCustomizationApi');
      const templatesData = await businessPlanTemplatesAPI.getTemplates();

      // Transform API data to match component interface
      const transformedTemplates: Template[] = templatesData.map(template => ({
        id: template.id,
        name: template.name,
        description: template.description,
        category: template.industry?.toLowerCase().replace(/\s+/g, '_') || 'general',
        type: template.template_type || 'standard',
        difficulty: template.difficulty_level || 'intermediate',
        estimatedTime: template.estimated_time || 8,
        sections: Object.keys(template.sections?.sections || {}).length || 0,
        popularity: template.popularity_score || 0, // Use real popularity score from API
        downloads: template.usage_count || 0,
        rating: template.rating || 0,
        isNew: template.is_new || false, // Use real is_new from API
        isPremium: template.is_premium || false, // Use real is_premium from API
        author: template.author_details?.username || template.author_details?.first_name || t("common.system", "System"),
        createdAt: template.created_at || new Date().toISOString(),
        tags: template.tags || [],
        isFavorite: userFavorites.includes(template.id)
      }));

      setTemplates(transformedTemplates);
    } catch (error) {
      console.error('Error loading templates:', error);
      setError(t('templates.library.loadError', 'Failed to load templates. Please try again later.'));
    } finally {
      setLoading(false);
    }
  };

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesDifficulty = selectedDifficulty === 'all' || template.difficulty === selectedDifficulty;

    return matchesSearch && matchesCategory && matchesDifficulty;
  });

  const sortedTemplates = [...filteredTemplates].sort((a, b) => {
    switch (sortBy) {
      case 'popularity':
        return b.popularity - a.popularity;
      case 'newest':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      case 'rating':
        return b.rating - a.rating;
      case 'downloads':
        return b.downloads - a.downloads;
      case 'name':
        return a.name.localeCompare(b.name);
      default:
        return 0;
    }
  });

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'text-green-400 bg-green-900/30';
      case 'intermediate': return 'text-yellow-400 bg-yellow-900/30';
      case 'advanced': return 'text-red-400 bg-red-900/30';
      default: return 'text-gray-400 bg-gray-900/30';
    }
  };

  const handleTemplateSelect = (template: Template) => {
    navigate(`/dashboard/templates/${template.id}/preview`);
  };

  const handleUseTemplate = (template: Template) => {
    navigate(`/dashboard/business-plans/new?template=${template.id}`);
  };

  const toggleFavorite = async (templateId: number) => {
    try {
      const { businessPlanTemplatesAPI } = await import('../../services/templateCustomizationApi');
      const isFavorite = userFavorites.includes(templateId);

      if (isFavorite) {
        await businessPlanTemplatesAPI.removeFromFavorites(templateId);
        setUserFavorites(prev => prev.filter(id => id !== templateId));
      } else {
        await businessPlanTemplatesAPI.addToFavorites(templateId);
        setUserFavorites(prev => [...prev, templateId]);
      }

      // Update templates state to reflect the change
      setTemplates(prev => prev.map(template =>
        template.id === templateId
          ? { ...template, isFavorite: !template.isFavorite }
          : template
      ));
    } catch (error) {
      console.error('Error toggling favorite:', error);
      // Show error message to user
      setError(t('templates.library.favoriteError', 'Failed to update favorites. Please try again.'));
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="p-6 space-y-6">
        {/* Header */}
        <div className={`flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <RTLText as="h1" className="text-3xl font-bold text-white mb-2">
              {t('templates.library.title')}
            </RTLText>
            <RTLText className="text-gray-300">
              {t('templates.library.description')}
            </RTLText>
          </div>

          <div className={`flex items-center gap-3 ${isRTL ? "flex-row-reverse" : ""}`}>
            <button
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              className="p-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors"
            >
              {viewMode === 'grid' ? <List size={20} /> : <Grid size={20} />}
            </button>

            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Filter size={16} />
              {t('common.filters')}
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className={`absolute top-1/2 transform -translate-y-1/2 ${isRTL ? 'right-3' : 'left-3'} text-gray-400`} size={20} />
            <input
              type="text"
              placeholder={t('templates.library.searchPlaceholder')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className={`w-full ${isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent`}
            />
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {t('templates.category')}
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full p-2 bg-white/20 border border-white/30 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name} ({category.count})
                    </option>
                  ))}
                </select>
              </div>

              {/* Difficulty Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {t('templates.difficulty')}
                </label>
                <select
                  value={selectedDifficulty}
                  onChange={(e) => setSelectedDifficulty(e.target.value)}
                  className="w-full p-2 bg-white/20 border border-white/30 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="all">{t('templates.difficulty.all')}</option>
                  <option value="beginner">{t('templates.difficulty.beginner')}</option>
                  <option value="intermediate">{t('templates.difficulty.intermediate')}</option>
                  <option value="advanced">{t('templates.difficulty.advanced')}</option>
                </select>
              </div>

              {/* Sort Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {t('templates.sortBy')}
                </label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full p-2 bg-white/20 border border-white/30 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  {sortOptions.map(option => (
                    <option key={option.id} value={option.id}>
                      {option.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}
        </div>

        {/* Results Count */}
        <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <RTLText className="text-gray-400">
            {t('templates.library.resultsCount', { count: sortedTemplates.length })}
          </RTLText>

          <div className={`flex items-center gap-2 text-sm text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
            <TrendingUp size={16} />
            {t('templates.library.sortedBy')} {sortOptions.find(opt => opt.id === sortBy)?.name}
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="bg-red-900/20 border border-red-500/50 rounded-lg p-4 mb-6">
            <div className={`flex items-center gap-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className="text-red-400">⚠️</div>
              <div>
                <RTLText className="text-red-400 font-medium">
                  {t('common.error', 'Error')}
                </RTLText>
                <RTLText className="text-red-300 text-sm mt-1">
                  {error}
                </RTLText>
              </div>
            </div>
            <button
              onClick={() => {
                setError(null);
                loadTemplates();
                loadCategories();
              }}
              className="mt-3 px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white text-sm transition-colors"
            >
              {t('common.retry', 'Try Again')}
            </button>
          </div>
        )}

        {/* Templates Grid/List */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white/10 backdrop-blur-sm rounded-lg p-6 animate-pulse border border-white/20">
                <div className="h-4 bg-white/20 rounded mb-4"></div>
                <div className="h-3 bg-white/20 rounded mb-2"></div>
                <div className="h-3 bg-white/20 rounded w-2/3"></div>
              </div>
            ))}
          </div>
        ) : (
          <div className={viewMode === 'grid'
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            : "space-y-4"}
          >
            {sortedTemplates.map(template => (
              <div
                key={template.id}
                className={`bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 hover:border-purple-500/50 transition-all duration-300 ${
                  viewMode === 'grid' ? 'p-6' : 'p-4 flex items-center gap-4'}
                }`}
              >
                {viewMode === 'grid' ? (
                  <>
                    {/* Grid View */}
                    <div className={`flex items-start justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`flex items-center gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                        {template.isNew && (
                          <span className="px-2 py-1 bg-green-600 text-white text-xs rounded-full">
                            {t('templates.new')}
                          </span>
                        )}
                        {template.isPremium && (
                          <Crown size={16} className="text-yellow-400" />
                        )}
                      </div>

                      <button
                        onClick={() => toggleFavorite(template.id)}
                        className={`p-1 rounded-full transition-colors ${
                          template.isFavorite ? 'text-red-400' : 'text-gray-400 hover:text-red-400'}
                        }`}
                      >
                        <Heart size={16} fill={template.isFavorite ? 'currentColor' : 'none'} />
                      </button>
                    </div>

                    <RTLText as="h3" className="text-lg font-semibold text-white mb-2">
                      {template.name}
                    </RTLText>

                    <RTLText className="text-gray-300 text-sm mb-4 line-clamp-2">
                      {template.description}
                    </RTLText>

                    <div className="space-y-3 mb-4">
                      <div className={`flex items-center justify-between text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                        <span className={`px-2 py-1 rounded-full text-xs ${getDifficultyColor(template.difficulty)}`}>
                          {t(`templates.difficulty.${template.difficulty}`)}
                        </span>
                        <div className={`flex items-center gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <Star size={14} className="text-yellow-400" fill="currentColor" />
                          <span className="text-gray-300">{template.rating}</span>
                        </div>
                      </div>

                      <div className={`flex items-center justify-between text-xs text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <div className={`flex items-center gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <Clock size={12} />
                          {template.estimatedTime}h
                        </div>
                        <div className={`flex items-center gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <Download size={12} />
                          {template.downloads}
                        </div>
                      </div>
                    </div>

                    <div className={`flex gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <button
                        onClick={() => handleTemplateSelect(template)}
                        className={`flex-1 px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded-md text-white text-sm transition-colors flex items-center justify-center gap-1 ${isRTL ? "flex-row-reverse" : ""}`}
                      >
                        <Eye size={14} />
                        {t('templates.preview')}
                      </button>
                      <button
                        onClick={() => handleUseTemplate(template)}
                        className={`flex-1 px-3 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white text-sm transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
                      >
                        {t('templates.use')}
                      </button>
                    </div>
                  </>
                ) : (
                  <>
                    {/* List View */}
                    <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`flex items-center gap-2 mb-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <RTLText as="h3" className="text-lg font-semibold text-white">
                          {template.name}
                        </RTLText>
                        {template.isNew && (
                          <span className="px-2 py-1 bg-green-600 text-white text-xs rounded-full">
                            {t('templates.new')}
                          </span>
                        )}
                        {template.isPremium && (
                          <Crown size={16} className="text-yellow-400" />
                        )}
                      </div>

                      <RTLText className="text-gray-300 text-sm mb-2">
                        {template.description}
                      </RTLText>

                      <div className={`flex items-center gap-4 text-xs text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <span className={`px-2 py-1 rounded-full ${getDifficultyColor(template.difficulty)}`}>
                          {t(`templates.difficulty.${template.difficulty}`)}
                        </span>
                        <div className={`flex items-center gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <Star size={12} className="text-yellow-400" fill="currentColor" />
                          {template.rating}
                        </div>
                        <div className={`flex items-center gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <Clock size={12} />
                          {template.estimatedTime}h
                        </div>
                        <div className={`flex items-center gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <Download size={12} />
                          {template.downloads}
                        </div>
                      </div>
                    </div>

                    <div className={`flex items-center gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <button
                        onClick={() => toggleFavorite(template.id)}
                        className={`p-2 rounded-full transition-colors ${
                          template.isFavorite ? 'text-red-400' : 'text-gray-400 hover:text-red-400'}
                        }`}
                      >
                        <Heart size={16} fill={template.isFavorite ? 'currentColor' : 'none'} />
                      </button>

                      <button
                        onClick={() => handleTemplateSelect(template)}
                        className={`px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded-md text-white text-sm transition-colors flex items-center gap-1 ${isRTL ? "flex-row-reverse" : ""}`}
                      >
                        <Eye size={14} />
                        {t('templates.preview')}
                      </button>

                      <button
                        onClick={() => handleUseTemplate(template)}
                        className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white text-sm transition-colors"
                      >
                        {t('templates.use')}
                      </button>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!loading && sortedTemplates.length === 0 && (
          <div className="text-center py-12">
            <BookOpen size={48} className="mx-auto text-gray-500 mb-4" />
            <RTLText as="h3" className="text-xl font-semibold text-gray-300 mb-2">
              {t('templates.library.noResults')}
            </RTLText>
            <RTLText className="text-gray-400 mb-6">
              {t('templates.library.noResultsDescription')}
            </RTLText>
            <button
              onClick={() => {
                setSearchQuery('');
                setSelectedCategory('all');
                setSelectedDifficulty('all');
              }}
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-white transition-colors"
            >
              {t('templates.library.clearFilters')}
            </button>
          </div>
        )}
      </div>
        </div>
      </div>
    </div>
  );
};

export default TemplateLibraryPage;
