{"incubator": {"title": "Incubator", "stage": "Stage", "status": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected", "active": "Active", "inactive": "Inactive"}, "updates": "Updates", "totalIdeas": "Total Ideas", "approvedIdeas": "Approved", "pendingIdeas": "Pending", "totalUpdates": "Total Updates", "myBusinessIdeas": "My Business Ideas", "manageYourIdeas": "Create and manage your business ideas", "createIdea": "Create Idea", "noIdeas": "No business ideas found. Create your first idea to get started!", "searchIdeas": "Search your business ideas...", "businessIdeas": "Business Ideas", "businessIdea": {"audiencePlaceholder": "Describe your target audience...", "audienceRequired": "Target audience is required", "businessModel": "Business Model", "businessModelPlaceholder": "How will your business make money?", "collaborators": "Collaborators", "currentStage": "Current Stage", "deleteConfirmMessage": "This action cannot be undone. All associated data will be permanently deleted.", "deleteConfirmTitle": "Delete Business Idea", "deleteError": "Failed to delete business idea", "description": "Description", "descriptionPlaceholder": "Describe your business idea in detail...", "editTitle": "Edit Business Idea", "loginRequired": "Please log in to view business ideas", "marketOpportunity": "Market Opportunity", "marketOpportunityPlaceholder": "What is the market opportunity?", "marketPlaceholder": "Describe the market opportunity...", "modelPlaceholder": "Describe your business model...", "notFound": "Business idea not found", "owner": "Owner", "problemPlaceholder": "What problem does your idea solve?", "problemRequired": "Problem statement is required", "problemStatement": "Problem Statement", "solutionDescription": "Solution Description", "solutionPlaceholder": "How does your idea solve the problem?", "solutionRequired": "Solution description is required", "stages": {"concept": "Concept Stage", "development": "Development Stage", "established": "Established Business", "scaling": "Scaling Stage", "validation": "Validation Stage"}, "submitError": "Failed to submit business idea", "submitIdea": "Submit Idea", "submitSuccess": "Business idea submitted successfully", "submitting": "Submitting...", "targetAudience": "Target Audience", "targetAudiencePlaceholder": "Who is your target audience?", "title": "Title", "titlePlaceholder": "Enter a compelling title for your business idea", "titleRequired": "Title is required", "updateError": "Failed to update business idea", "moderation": {"pending": "Pending Review", "approved": "Approved", "rejected": "Rejected"}}, "createBusinessIdea": "Create Business Idea", "titlePlaceholder": "Enter your business idea title", "problemStatement": "Problem Statement", "problemPlaceholder": "What problem does your business idea solve?", "solutionDescription": "Solution Description", "solutionPlaceholder": "How does your idea solve the problem?", "targetAudience": "Target Audience", "targetAudiencePlaceholder": "Who is your target audience?", "marketOpportunity": "Market Opportunity", "marketOpportunityPlaceholder": "What is the market opportunity?", "businessModel": "Business Model", "businessModelPlaceholder": "How will your business make money?", "currentStage": "Current Stage", "stages": {"concept": "Concept Stage", "validation": "Validation Stage", "development": "Development Stage", "scaling": "Scaling Stage", "established": "Established Business"}, "achievements": "Achievements", "thisMonth": "This Month", "activeIdeas": "Active Ideas", "avgPerIdea": "Avg per Idea", "trackYourProgress": "Track your business idea progress", "createUpdate": "Create Update", "noBusinessIdeasWarning": "You need to create a business idea first before adding progress updates.", "searchUpdates": "Search your progress updates...", "createProgressUpdate": "Create Progress Update", "updateTitle": "Update Title", "updateTitlePlaceholder": "Enter a title for this update", "description": "Transform your entrepreneurial vision into reality with our comprehensive business incubator platform", "descriptionPlaceholder": "Provide a brief description of this update", "achievementsPlaceholder": "What milestones have you achieved?", "challenges": "Challenges", "challengesPlaceholder": "What challenges are you facing? (Optional)", "nextSteps": "Next Steps", "nextStepsPlaceholder": "What are your next steps?", "myIdeas": "My Ideas", "progressUpdates": {"title": "Progress Updates", "noUpdates": "No progress updates found. Create your first update to track your progress!"}, "resources": {"title": "Resources", "description": "Access incubator resources and materials"}, "mentorship": {"dashboard": "Mentorship Dashboard", "sessions": "Mentorship Sessions", "findMentor": "Find a <PERSON>tor", "scheduleMeeting": "Schedule Meeting"}, "funding": {"title": "Funding", "opportunities": "Funding Opportunities", "applications": "My Applications", "status": "Application Status"}, "businessPlan": {"title": "Business Plans", "create": "Create Business Plan", "templates": "Business Plan Templates", "myPlans": "My Business Plans", "createNewPlan": "Create New Plan", "businessIdea": "Business Idea", "selectBusinessIdea": "Select Business Idea", "planTitle": "Plan Title", "enterPlanTitle": "Enter Plan Title", "template": "Template", "selectTemplate": "Select Template", "generateCustomTemplate": "Generate Custom Template"}, "ideas": {"title": "Business Ideas", "create": "Create New Idea", "edit": "Edit Idea", "delete": "Delete Idea", "status": "Status", "category": "Category", "description": "Description"}, "progress": {"title": "Progress Tracking", "milestones": "Milestones", "updates": "Updates", "timeline": "Timeline"}, "adjustFilters": "Adjust Filters", "aiPowered": {"description": "AI-Powered Business Analysis", "errorGenerating": "Error generating analysis", "generateCompletePlan": "Generate Complete Plan", "generateMarketAnalysis": "Generate Market Analysis", "generating": "Generating...", "generatingDescription": "AI is analyzing your business idea...", "marketAnalysisPreview": "Market Analysis Preview", "planGenerated": "Plan Generated Successfully", "title": "AI-Powered Analysis"}, "analytics": {"accessDenied": "Access Denied", "advancedAnalytics": "Advanced Analytics", "analyticsNotAvailable": "Analytics not available", "areasForImprovement": "Areas for Improvement", "backToBusinessIdeas": "Back to Business Ideas", "businessIdeaNotFound": "Business idea not found", "comparative": {"areasToImprove": "Areas to Improve", "average": "Average", "business": "Business", "competitiveAdvantages": "Competitive Advantages", "goals": "Goals", "impact": "Impact", "industryAverage": "Industry Average", "industryAverages": "Industry Averages", "industryAveragesDescription": "Compare your performance with industry standards", "milestones": "Milestones", "noSimilarBusinesses": "No similar businesses found", "percentileDescription": "Your ranking compared to similar businesses", "percentileRankings": "Percentile Rankings", "progress": "Progress", "similarBusinesses": "Similar Businesses", "stage": "Stage", "suggestion": "Suggestion", "title": "Comparative Analysis", "yourBusiness": "Your Business", "yourValue": "Your Value"}, "competitive": {"competitiveAdvantages": "Competitive Advantages", "competitiveDisadvantages": "Competitive Disadvantages", "competitorAnalysis": "Competitor Analysis", "currentMarketTrends": "Current Market Trends", "emergingTrends": "Emerging Trends", "failedToGenerateCompetitive": "Failed to generate competitive analysis", "failedToGenerateTrends": "Failed to generate trends analysis", "failedToLoadCompetitive": "Failed to load competitive analysis", "failedToLoadTrends": "Failed to load trends analysis", "generateAnalysis": "Generate Analysis", "generating": "Generating analysis...", "loadMarketTrends": "Load Market Trends", "loadTrends": "Load Trends", "marketOverview": "Market Overview", "marketTrendsAnalysis": "Market Trends Analysis", "noAnalysisAvailable": "No analysis available", "noTrendsAvailable": "No trends available", "refreshAnalysis": "Refresh Analysis", "refreshTrends": "Refresh Trends", "strategicRecommendations": "Strategic Recommendations", "title": "Competitive Analysis", "trendImpactAnalysis": "Trend Impact Analysis"}, "competitiveAnalysis": "Competitive Analysis", "continueWorkingToStrengths": "Continue working to your strengths", "dashboard": "Analytics Dashboard", "errorLoadingAnalytics": "Error loading analytics", "errorLoadingBusinessIdea": "Error loading business idea", "exportPDF": "Export PDF", "failedToLoadAnalytics": "Failed to load analytics", "failedToRefreshAnalytics": "Failed to refresh analytics", "generalAnalytics": "General Analytics", "goalAchievement": "Goal Achievement", "goalsAchieved": "Goals Achieved", "healthScore": "Health Score", "historical": {"date": "Date", "goalAchievement": "Goal Achievement", "historicalTrends": "Historical Trends", "milestoneCompletion": "Milestone Completion", "progressRate": "Progress Rate", "title": "Historical Analysis"}, "keepUpGoodWork": "Keep up the good work", "keyMetrics": "Key Metrics", "keyStrengths": "Key Strengths", "lastUpdated": "Last Updated", "mentorEngagement": "Mentor Engagement", "milestoneCompletion": "Milestone Completion", "milestonesCompleted": "Milestones Completed", "noAnalyticsAvailable": "No analytics available", "noKeyStrengthsYet": "No key strengths identified yet", "noSignificantWeaknesses": "No significant weaknesses identified", "outOf100": "out of 100", "overallBusinessHealth": "Overall Business Health", "predictive": {"action": "Action", "confidence": "Confidence", "createMilestones": "Create milestones to get predictions", "daysOverdue": "days overdue", "daysRemaining": "days remaining", "goalAchievement": "Goal Achievement", "growthProjections": "Growth Projections", "growthProjectionsDescription": "Projected growth based on current trends", "keyOpportunities": "Key Opportunities", "keyRiskFactors": "Key Risk Factors", "milestoneCompletion": "Milestone Completion", "milestonePredictions": "Milestone Predictions", "mitigation": "Mitigation", "noMilestonePredictions": "No milestone predictions available", "predictedCompletion": "Predicted Co<PERSON>tion", "progressRate": "Progress Rate", "successProbability": "Success Probability", "title": "Predictive Analysis"}, "progressRate": "Progress Rate", "progressUpdates": "Progress Updates", "recentActivity30Days": "Recent Activity (30 days)", "recommendations": {"high": "High Priority", "keepUpGoodWork": "Keep up the good work!", "low": "Low Priority", "medium": "Medium Priority", "noRecommendations": "No recommendations available", "priority": "Priority", "title": "Recommendations"}, "refreshData": "Refresh Data", "refreshing": "Refreshing...", "retry": "Retry", "teamSize": "Team Size", "totalActivities": "Total Activities", "updatesPerMonth": "Updates per Month"}, "applyForMentorship": "Apply for Mentorship", "beFirstToSubmit": "Be the first to submit an idea!", "businessIdeaCard": {"collaborators": "Collaborators", "viewDetails": "View Details"}, "businessIdeasDescription": "Explore and develop innovative business ideas", "businessIdeasTitle": "Business Ideas", "businessPlanning": "Business Planning", "businessPlanningDesc": "Create comprehensive business plans", "businessTools": "Business Tools", "businessToolsDesc": "Access essential business development tools", "communityFeedback": "Community Feedback", "communityFeedbackDesc": "Get feedback from the entrepreneurial community", "deleteIdea": "Delete Idea", "errors": {"failedToCreate": "Failed to create", "failedToDelete": "Failed to delete", "failedToLoad": "Failed to load"}, "expertGuidance": "Expert Guidance", "expertGuidanceDesc": "Get guidance from experienced mentors and advisors"}}