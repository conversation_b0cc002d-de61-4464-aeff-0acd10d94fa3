/**
 * Template Analytics Page
 * Comprehensive analytics dashboard for template performance and usage
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { BarChart3, TrendingUp, Users, Target, Loader2 } from 'lucide-react';
// AuthenticatedLayout removed - page is already wrapped by route layout
import TemplateAnalyticsDashboard from '../../components/analytics/TemplateAnalyticsDashboard';
import { RTLText, RTLFlex } from '../../components/common';
import { templateAnalyticsAPI } from '../../services/templateAnalyticsApi';

const TemplateAnalyticsPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadAnalyticsData = async () => {
      try {
        setLoading(true);
        const data = await templateAnalyticsAPI.getDashboardData({
          timeRange: '30d'
        });
        setAnalyticsData(data);
      } catch (err) {
        console.error('Error loading analytics data:', err);
        setError('Failed to load analytics data');
      } finally {
        setLoading(false);
      }
    };

    loadAnalyticsData();
  }, []);

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-6">
        {/* Page Header */}
        <div className="bg-gray-800/50 rounded-lg p-6">
          <RTLFlex className="items-center justify-between">
            <div>
              <RTLFlex className="items-center mb-2">
                <BarChart3 className={`text-purple-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} size={28} />
                <RTLText as="h1" className="text-2xl font-bold">
                  {t('analytics.title')}
                </RTLText>
              </RTLFlex>
              <p className="text-gray-300">
                {t('analytics.description')}
              </p>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-3 gap-4">
              {loading ? (
                <div className="col-span-3 flex justify-center">
                  <Loader2 className="animate-spin text-purple-400" size={24} />
                </div>
              ) : error ? (
                <div className="col-span-3 text-center text-red-400 text-sm">
                  {error}
                </div>
              ) : (
                <>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-400">
                      {analyticsData?.overview?.total_templates || 0}
                    </div>
                    <div className="text-sm text-gray-400">{t('analytics.totalTemplates', 'Total Templates')}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-400">
                      {analyticsData?.overview?.total_usage ?
                        (analyticsData.overview.total_usage > 1000 ?
                          `${(analyticsData.overview.total_usage / 1000).toFixed(1)}K` :
                          analyticsData.overview.total_usage) : 0}
                    </div>
                    <div className="text-sm text-gray-400">{t('analytics.totalUsage', 'Total Usage')}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-400">
                      {analyticsData?.overview?.average_rating ?
                        analyticsData.overview.average_rating.toFixed(1) : '0.0'}
                    </div>
                    <div className="text-sm text-gray-400">{t('analytics.avgRating', 'Avg Rating')}</div>
                  </div>
                </>
              )}
            </div>
          </RTLFlex>
        </div>

        {/* Analytics Dashboard */}
        <TemplateAnalyticsDashboard />
      </div>
        </div>
      </div>
    </div>
  );
};

export default TemplateAnalyticsPage;
