/**
 * Template Creation Page
 * Comprehensive template creation interface with AI assistance
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  FileText,
  Plus,
  Save,
  ArrowLeft,
  Sparkles,
  Brain,
  Wand2,
  Eye,
  Settings,
  Upload,
  Download,
  Copy,
  Trash2,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  GripVertical,
  Move
} from 'lucide-react';
// MainLayout removed - handled by routing system
import { TemplateCustomizer } from '../../components/incubator';
import { RTLText, RTLFlex, RichTextEditor } from '../../components/common';
import TemplateSelector from '../../components/templates/TemplateSelector';
import TemplateViewer from '../../components/templates/TemplateViewer';
import OfflineIndicator from '../../components/common/OfflineIndicator';
import { useLanguage } from '../../hooks/useLanguage';
import {
  BusinessPlanTemplate,
  CustomBusinessPlanTemplate,
  businessPlanTemplatesAPI,
  customTemplatesAPI
} from '../../services/templateCustomizationApi';

interface TemplateSection {
  id: string;
  title: string;
  description: string;
  content: string;
  isRequired: boolean;
  order: number;
  type: 'text' | 'list' | 'table' | 'chart';
  aiPrompt?: string;
}

const TemplateCreationPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // State management
  const [currentStep, setCurrentStep] = useState<'browse' | 'basic' | 'sections' | 'customize' | 'preview'>('browse');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Template browsing and preview
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewTemplateId, setPreviewTemplateId] = useState<string>('');
  const [selectedTemplateForBase, setSelectedTemplateForBase] = useState<string>('');

  // Template data
  const [templateName, setTemplateName] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');
  const [templateIndustry, setTemplateIndustry] = useState('');
  const [templateType, setTemplateType] = useState<'standard' | 'lean' | 'detailed' | 'custom'>('standard');
  const [baseTemplate, setBaseTemplate] = useState<BusinessPlanTemplate | null>(null);
  const [sections, setSections] = useState<TemplateSection[]>([]);
  const [availableTemplates, setAvailableTemplates] = useState<BusinessPlanTemplate[]>([]);

  // Drag and drop state
  const [draggedSection, setDraggedSection] = useState<string | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);

  // Check if editing existing template
  const templateId = searchParams.get('template');
  const isEditing = !!templateId;

  useEffect(() => {
    loadAvailableTemplates();
    if (isEditing && templateId) {
      loadExistingTemplate(templateId);
    }
  }, [templateId]);

  const loadAvailableTemplates = async () => {
    try {
      const templates = await businessPlanTemplatesAPI.getTemplates();
      setAvailableTemplates(templates);
    } catch (error) {
      console.error('Error loading templates:', error);
    }
  };

  const loadExistingTemplate = async (id: string) => {
    try {
      setIsLoading(true);
      const template = await customTemplatesAPI.getCustomTemplate(parseInt(id));

      setTemplateName(template.name);
      setTemplateDescription(template.description);
      setBaseTemplate(template.base_template_details);

      // Convert template sections to our format
      const convertedSections: TemplateSection[] = Object.entries(template.sections || {}).map(([key, section]: [string, any], index) => ({
        id: key,
        title: section.title || key,
        description: section.description || '',
        content: section.content || '',
        isRequired: section.required || false,
        order: index,
        type: section.type || 'text',
        aiPrompt: section.ai_prompt
      }));

      setSections(convertedSections);
    } catch (error) {
      console.error('Error loading template:', error);
      setError(t('templates.failedToLoadTemplate'));
    } finally {
      setIsLoading(false);
    }
  };

  const generateAITemplate = async () => {
    if (!templateIndustry.trim()) {
      setError(t('templates.industryRequired'));
      return;
    }

    setIsLoading(true);
    try {
      const generatedTemplate = await businessPlanTemplatesAPI.generateTemplate(templateIndustry);

      // Convert generated template to our format
      const generatedSections: TemplateSection[] = Object.entries(generatedTemplate.sections || {}).map(([key, section]: [string, any], index) => ({
        id: key,
        title: section.title || key,
        description: section.description || '',
        content: section.content || '',
        isRequired: section.required || false,
        order: index,
        type: section.type || 'text',
        aiPrompt: section.ai_prompt
      }));

      setSections(generatedSections);
      setTemplateName(generatedTemplate.name);
      setTemplateDescription(generatedTemplate.description);
      setCurrentStep('sections');
      setSuccess(t('templates.aiTemplateGenerated'));
    } catch (error) {
      console.error('Error generating AI template:', error);
      setError(t('templates.failedToGenerateTemplate'));
    } finally {
      setIsLoading(false);
    }
  };

  const addSection = () => {
    const newSection: TemplateSection = {
      id: `section_${Date.now()}`,
      title: t('templates.newSection'),
      description: '',
      content: '',
      isRequired: false,
      order: sections.length,
      type: 'text'
    };
    setSections([...sections, newSection]);
  };

  const updateSection = (id: string, updates: Partial<TemplateSection>) => {
    setSections(sections.map(section =>
      section.id === id ? { ...section, ...updates } : section
    ));
  };

  const removeSection = (id: string) => {
    setSections(sections.filter(section => section.id !== id));
  };

  const reorderSections = (fromIndex: number, toIndex: number) => {
    const reordered = [...sections];
    const [moved] = reordered.splice(fromIndex, 1);
    reordered.splice(toIndex, 0, moved);

    // Update order numbers
    const updated = reordered.map((section, index) => ({
      ...section,
      order: index
    }));

    setSections(updated);
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent, sectionId: string) => {
    setDraggedSection(sectionId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverIndex(index);
  };

  const handleDragLeave = () => {
    setDragOverIndex(null);
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();

    if (!draggedSection) return;

    const dragIndex = sections.findIndex(s => s.id === draggedSection);
    if (dragIndex !== -1 && dragIndex !== dropIndex) {
      reorderSections(dragIndex, dropIndex);
    }

    setDraggedSection(null);
    setDragOverIndex(null);
  };

  const saveTemplate = async () => {
    if (!templateName.trim()) {
      setError(t('templates.nameRequired'));
      return;
    }

    setSaving(true);
    try {
      const templateData = {
        name: templateName,
        description: templateDescription,
        base_template: baseTemplate?.id,
        sections: sections.reduce((acc, section) => {
          acc[section.id] = {
            title: section.title,
            description: section.description,
            content: section.content,
            required: section.isRequired,
            type: section.type,
            order: section.order,
            ai_prompt: section.aiPrompt
          };
          return acc;
        }, {} as any),
        is_public: false
      };

      if (isEditing && templateId) {
        await customTemplatesAPI.updateCustomTemplate(parseInt(templateId), templateData);
        setSuccess(t('templates.templateUpdated'));
      } else {
        await customTemplatesAPI.createCustomTemplate(templateData);
        setSuccess(t('templates.templateCreated'));
      }

      // Navigate back to templates overview after a delay
      setTimeout(() => {
        navigate('/dashboard/templates');
      }, 2000);

    } catch (error) {
      console.error('Error saving template:', error);
      setError(t('templates.failedToSaveTemplate'));
    } finally {
      setSaving(false);
    }
  };

  const renderBrowseStep = () => (
    <div className="space-y-6">
      <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <RTLText as="h2" className="text-2xl font-bold mb-2">
            Choose a Base Template
          </RTLText>
          <p className="text-gray-300">
            Select an existing template to customize or start from scratch
          </p>
        </div>

        <OfflineIndicator showDetails={false} />
      </div>

      <TemplateSelector
        onSelectTemplate={setSelectedTemplateForBase}
        selectedTemplate={selectedTemplateForBase}
        showCategories={true}
        showFilters={true}
        showActionButtons={true}
        onPreviewTemplate={(templateId) => {
          setPreviewTemplateId(templateId);
          setShowPreviewModal(true);
        }}
        onUseTemplate={(templateId) => {
          setSelectedTemplateForBase(templateId);
          // Find the selected template and set it as base
          const selectedTemplate = availableTemplates.find(t => t.id.toString() === templateId);
          if (selectedTemplate) {
            setBaseTemplate(selectedTemplate);
            setTemplateName(`Custom ${selectedTemplate.name}`);
            setTemplateDescription(`Customized version of ${selectedTemplate.name}`);
          }
          setCurrentStep('basic');
        }}
        onCreateTemplate={() => {
          // Start from scratch
          setBaseTemplate(null);
          setTemplateName('');
          setTemplateDescription('');
          setCurrentStep('basic');
        }}
      />

      <div className="text-center">
        <button
          onClick={() => {
            setBaseTemplate(null);
            setCurrentStep('basic');
          }}
          className="px-6 py-3 bg-gray-600 hover:bg-gray-700 rounded-lg text-white transition-colors"
        >
          Start from Scratch
        </button>
      </div>
    </div>
  );

  const renderSectionsStep = () => (
    <div className="space-y-6">
      <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <RTLText as="h2" className="text-2xl font-bold mb-2">
            {t('templates.sections')}
          </RTLText>
          <p className="text-gray-300">
            Configure the sections for your template
          </p>
        </div>
        <button
          onClick={addSection}
          className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <Plus size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
          Add Section
        </button>
      </div>

      <div className="space-y-4">
        {sections.map((section, index) => (
          <div
            key={section.id}
            className={`bg-gray-700/50 rounded-lg p-4 border border-gray-600 transition-all duration-200 ${
              dragOverIndex === index ? 'border-purple-400 bg-purple-900/20' : ''
            } ${draggedSection === section.id ? 'opacity-50' : ''}`}
            draggable
            onDragStart={(e) => handleDragStart(e, section.id)}
            onDragOver={(e) => handleDragOver(e, index)}
            onDragLeave={handleDragLeave}
            onDrop={(e) => handleDrop(e, index)}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <GripVertical
                  size={16}
                  className="text-gray-400 hover:text-white cursor-grab mr-2"
                  title={t('templates.dragToReorder', 'Drag to reorder')}
                />
                <span className="text-sm text-gray-400">Section {index + 1}</span>
              </div>
              <button
                onClick={() => removeSection(section.id)}
                className="text-red-400 hover:text-red-300 p-1"
                title={t('templates.removeSection', 'Remove section')}
              >
                <Trash2 size={16} />
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium mb-2">{t("dashboard.section.title", "Section Title")}</label>
                <input
                  type="text"
                  value={section.title}
                  onChange={(e) => updateSection(section.id, { title: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:ring-2 focus:ring-purple-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">{t("dashboard.section.type", "Section Type")}</label>
                <select
                  value={section.type}
                  onChange={(e) => updateSection(section.id, { type: e.target.value as any })}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:ring-2 focus:ring-purple-500"
                >
                  <option value="text">{t("dashboard.text", "Text")}</option>
                  <option value="list">{t("dashboard.list", "List")}</option>
                  <option value="table">{t("dashboard.table", "Table")}</option>
                  <option value="chart">{t("dashboard.chart", "Chart")}</option>
                </select>
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">{t("dashboard.description", t("common.description", "Description"))}</label>
              <textarea
                value={section.description}
                onChange={(e) => updateSection(section.id, { description: e.target.value })}
                rows={2}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:ring-2 focus:ring-purple-500"
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">AI Prompt (Optional)</label>
              <textarea
                value={section.aiPrompt || ''}
                onChange={(e) => updateSection(section.id, { aiPrompt: e.target.value })}
                rows={2}
                placeholder={t("dashboard.enter.ai.prompt", "Enter AI prompt for generating content for this section...")}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:ring-2 focus:ring-purple-500"
              />
            </div>

            <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <label className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <input
                  type="checkbox"
                  checked={section.isRequired}
                  onChange={(e) => updateSection(section.id, { isRequired: e.target.checked })}
                  className={`mr-2 rounded ${isRTL ? "space-x-reverse" : ""}`}
                />
                Required Section
              </label>
              <button
                onClick={() => removeSection(section.id)}
                className="text-red-400 hover:text-red-300 p-2"
              >
                <Trash2 size={16} />
              </button>
            </div>
          </div>
        ))}
      </div>

      <RTLFlex className="justify-between">
        <button
          onClick={() => setCurrentStep('basic')}
          className={`px-6 py-2 bg-gray-600 hover:bg-gray-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <ArrowLeft size={16} className={`${isRTL ? 'ml-2' : 'mr-2'} ${isRTL ? 'rotate-180' : ''}`} />
          {t('common.back')}
        </button>
        <button
          onClick={() => setCurrentStep('customize')}
          className={`px-6 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          {t('common.next')}
          <ArrowLeft size={16} className={`${isRTL ? 'mr-2' : 'ml-2'} ${isRTL ? '' : 'rotate-180'}`} />
        </button>
      </RTLFlex>
    </div>
  );

  const renderCustomizeStep = () => (
    <div className="space-y-6">
      <div>
        <RTLText as="h2" className="text-2xl font-bold mb-2">
          Customize Template
        </RTLText>
        <p className="text-gray-300">
          Fine-tune your template settings and appearance
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">t("dashboard.template.settings", "Template Settings")</h3>

          <div>
            <label className="block text-sm font-medium mb-2">t("dashboard.template.color.theme", "Template Color Theme")</label>
            <select className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:ring-2 focus:ring-purple-500">
              <option value="default">t("dashboard.default", "Default")</option>
              <option value="professional">t("dashboard.professional.blue", "Professional Blue")</option>
              <option value="creative">t("dashboard.creative.purple", "Creative Purple")</option>
              <option value="modern">t("dashboard.modern.green", "Modern Green")</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">t("dashboard.default.font", "Default Font")</label>
            <select className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:ring-2 focus:ring-purple-500">
              <option value="inter">t("dashboard.inter", "Inter")</option>
              <option value="roboto">t("dashboard.roboto", "Roboto")</option>
              <option value="opensans">t("dashboard.open.sans", "Open Sans")</option>
              <option value="lato">t("dashboard.lato", "Lato")</option>
            </select>
          </div>

          <div>
            <label className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <input type="checkbox" className={`mr-2 rounded ${isRTL ? "space-x-reverse" : ""}`} />
              Include cover page
            </label>
          </div>

          <div>
            <label className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <input type="checkbox" className={`mr-2 rounded ${isRTL ? "space-x-reverse" : ""}`} />
              Include table of contents
            </label>
          </div>

          <div>
            <label className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <input type="checkbox" className={`mr-2 rounded ${isRTL ? "space-x-reverse" : ""}`} />
              Include appendix section
            </label>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">t("dashboard.ai.assistance", "AI Assistance")</h3>

          <div>
            <label className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <input type="checkbox" className={`mr-2 rounded ${isRTL ? "space-x-reverse" : ""}`} defaultChecked />
              Enable AI content suggestions
            </label>
          </div>

          <div>
            <label className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <input type="checkbox" className={`mr-2 rounded ${isRTL ? "space-x-reverse" : ""}`} defaultChecked />
              Auto-generate section outlines
            </label>
          </div>

          <div>
            <label className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <input type="checkbox" className={`mr-2 rounded ${isRTL ? "space-x-reverse" : ""}`} />
              Include industry-specific guidance
            </label>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">t("dashboard.ai.tone", "AI Tone")</label>
            <select className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:ring-2 focus:ring-purple-500">
              <option value="professional">t("dashboard.professional", "Professional")</option>
              <option value="friendly">t("dashboard.friendly", "Friendly")</option>
              <option value="formal">t("dashboard.formal", "Formal")</option>
              <option value="creative">t("dashboard.creative", "Creative")</option>
            </select>
          </div>
        </div>
      </div>

      <RTLFlex className="justify-between">
        <button
          onClick={() => setCurrentStep('sections')}
          className={`px-6 py-2 bg-gray-600 hover:bg-gray-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <ArrowLeft size={16} className={`${isRTL ? 'ml-2' : 'mr-2'} ${isRTL ? 'rotate-180' : ''}`} />
          {t('common.back')}
        </button>
        <button
          onClick={() => setCurrentStep('preview')}
          className={`px-6 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          {t('common.next')}
          <ArrowLeft size={16} className={`${isRTL ? 'mr-2' : 'ml-2'} ${isRTL ? '' : 'rotate-180'}`} />
        </button>
      </RTLFlex>
    </div>
  );

  const renderPreviewStep = () => (
    <div className="space-y-6">
      <div>
        <RTLText as="h2" className="text-2xl font-bold mb-2">
          Preview Template
        </RTLText>
        <p className="text-gray-300">
          Review your template before saving
        </p>
      </div>

      <div className="bg-white text-black rounded-lg p-6 max-h-96 overflow-y-auto">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold mb-2">{templateName || t("dashboard.template.name", "Template Name")}</h1>
          <p className="text-gray-600">{templateDescription || t("dashboard.template.description.will", "Template description will appear here")}</p>
          <div className="text-sm text-gray-500 mt-2">
            Industry: {templateIndustry || t("dashboard.not.specified", "Not specified")} | Type: {templateType}
          </div>
        </div>

        <div className="space-y-4">
          {sections.map((section, index) => (
            <div key={section.id} className="border-l-4 border-purple-500 pl-4">
              <h3 className="text-lg font-semibold mb-2">
                {index + 1}. {section.title}
                {section.isRequired && <span className={`text-red-500 ml-1 ${isRTL ? "space-x-reverse" : ""}`}>*</span>}
              </h3>
              <p className="text-gray-600 text-sm mb-2">{section.description}</p>
              <div className="text-xs text-gray-500">
                Type: {section.type} | {section.aiPrompt ? t("dashboard.aiassisted.manual.entry", "AI-assisted") : 'Manual entry'}
              </div>
            </div>
          ))}
        </div>

        {sections.length === 0 && (
          <div className="text-center text-gray-500 py-8">
            No sections added yet. Go back to add sections to your template.
          </div>
        )}
      </div>

      <RTLFlex className="justify-between">
        <button
          onClick={() => setCurrentStep('customize')}
          className={`px-6 py-2 bg-gray-600 hover:bg-gray-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <ArrowLeft size={16} className={`${isRTL ? 'ml-2' : 'mr-2'} ${isRTL ? 'rotate-180' : ''}`} />
          {t('common.back')}
        </button>
        <button
          onClick={saveTemplate}
          disabled={isSaving || !templateName.trim()}
          className={`px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-800/50 disabled:cursor-not-allowed rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          {isSaving ? (
            <RefreshCw size={16} className={`${isRTL ? 'ml-2' : 'mr-2'} animate-spin`} />
          ) : (
            <Save size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
          )}
          {isSaving ? t('templates.saving') : t('templates.saveTemplate')}
        </button>
      </RTLFlex>
    </div>
  );

  const renderBasicInfoStep = () => (
    <div className="space-y-6">
      <div>
        <RTLText as="h2" className="text-2xl font-bold mb-4">
          {t('templates.basicInformation')}
        </RTLText>
        <p className="text-gray-300 mb-6">
          {t('templates.basicInfoDescription')}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium mb-2">
            {t('templates.templateName')} *
          </label>
          <input
            type="text"
            value={templateName}
            onChange={(e) => setTemplateName(e.target.value)}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            placeholder={t('templates.templateNamePlaceholder')}
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            {t('templates.industry')}
          </label>
          <input
            type="text"
            value={templateIndustry}
            onChange={(e) => setTemplateIndustry(e.target.value)}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            placeholder={t('templates.industryPlaceholder')}
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">
          {t('templates.description')}
        </label>
        <textarea
          value={templateDescription}
          onChange={(e) => setTemplateDescription(e.target.value)}
          rows={4}
          className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          placeholder={t('templates.descriptionPlaceholder')}
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">
          {t('templates.templateType')}
        </label>
        <select
          value={templateType}
          onChange={(e) => setTemplateType(e.target.value as any)}
          className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
        >
          <option value="standard">{t('templates.types.standard')}</option>
          <option value="lean">{t('templates.types.lean')}</option>
          <option value="detailed">{t('templates.types.detailed')}</option>
          <option value="investor">{t('templates.types.investor')}</option>
          <option value="startup">{t('templates.types.startup')}</option>
          <option value="saas">{t('templates.types.saas')}</option>
          <option value="ecommerce">{t('templates.types.ecommerce')}</option>
          <option value="restaurant">{t('templates.types.restaurant')}</option>
          <option value="consulting">{t('templates.types.consulting')}</option>
          <option value="mobile_app">{t('templates.types.mobileApp')}</option>
          <option value="nonprofit">{t('templates.types.nonprofit')}</option>
          <option value="retail">{t('templates.types.retail')}</option>
          <option value="manufacturing">{t('templates.types.manufacturing')}</option>
          <option value="healthcare">{t('templates.types.healthcare')}</option>
          <option value="education">{t('templates.types.education')}</option>
          <option value="fintech">{t('templates.types.fintech')}</option>
          <option value="marketplace">{t('templates.types.marketplace')}</option>
          <option value="subscription">{t('templates.types.subscription')}</option>
          <option value="social_impact">{t('templates.types.socialImpact')}</option>
          <option value="green_business">{t('templates.types.greenBusiness')}</option>
          <option value="custom">{t('templates.types.custom')}</option>
        </select>
      </div>

      {/* AI Generation Option */}
      <div className="bg-gradient-to-r from-purple-900/30 to-pink-900/30 rounded-lg p-6 border border-purple-500/30">
        <RTLFlex className="items-center mb-4">
          <Brain className={isRTL ? 'ml-3' : 'mr-3'} size={24} />
          <div>
            <RTLText as="h3" className="font-semibold">
              {t('templates.aiGeneration')}
            </RTLText>
            <p className="text-sm text-gray-300">
              {t('templates.aiGenerationDescription')}
            </p>
          </div>
        </RTLFlex>

        <button
          onClick={generateAITemplate}
          disabled={isLoading || !templateIndustry.trim()}
          className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800/50 disabled:cursor-not-allowed rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          {isLoading ? (
            <RefreshCw size={16} className={`${isRTL ? 'ml-2' : 'mr-2'} animate-spin`} />
          ) : (
            <Wand2 size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
          )}
          {isLoading ? t('templates.generating') : t('templates.generateWithAI')}
        </button>
      </div>

      <RTLFlex className="justify-between">
        <button
          onClick={() => setCurrentStep('browse')}
          className={`px-6 py-2 bg-gray-600 hover:bg-gray-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <ArrowLeft size={16} className={`${isRTL ? 'ml-2' : 'mr-2'} ${isRTL ? 'rotate-180' : ''}`} />
          {t('common.back')}
        </button>
        <button
          onClick={() => setCurrentStep('sections')}
          disabled={!templateName.trim()}
          className={`px-6 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800/50 disabled:cursor-not-allowed rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          {t('common.next')}
          <ArrowLeft size={16} className={`${isRTL ? 'mr-2' : 'ml-2'} ${isRTL ? '' : 'rotate-180'}`} />
        </button>
      </RTLFlex>
    </div>
  );

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-6">
        {/* Header */}
        <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            onClick={() => navigate('/dashboard/templates')}
            className={`text-purple-400 hover:text-purple-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <ArrowLeft size={16} className={isRTL ? 'ml-1' : 'mr-1'} />
            {t('common.back')}
          </button>

          <RTLText as="h1" className="text-3xl font-bold">
            {isEditing ? t('templates.editTemplate') : t('templates.createTemplate')}
          </RTLText>
        </div>

        {/* Progress Steps */}
        <div className={`flex items-center justify-center space-x-4 mb-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          {['browse', 'basic', 'sections', 'customize', 'preview'].map((step, index) => (
            <div key={step} className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className={`
                w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                ${currentStep === step
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-700 text-gray-300'}
                }
              `}>
                {index + 1}
              </div>
              <span className={`ml-2 text-sm ${
                currentStep === step ? 'text-white' : 'text-gray-400'}
              }`}>
                {step === 'browse' ? 'Browse' : t(`templates.steps.${step}`)}
              </span>
              {index < 4 && (
                <div className="w-8 h-0.5 bg-gray-700 mx-4" />
              )}
            </div>
          ))}
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className={`bg-red-900/50 border border-red-500/50 rounded-lg p-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <AlertCircle size={20} className={isRTL ? 'ml-3' : 'mr-3'} />
            <span>{error}</span>
          </div>
        )}

        {success && (
          <div className={`bg-green-900/50 border border-green-500/50 rounded-lg p-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <CheckCircle size={20} className={isRTL ? 'ml-3' : 'mr-3'} />
            <span>{success}</span>
          </div>
        )}

        {/* Step Content */}
        <div className="bg-gray-800/50 rounded-lg p-6">
          {currentStep === 'browse' && renderBrowseStep()}
          {currentStep === 'basic' && renderBasicInfoStep()}
          {currentStep === 'sections' && renderSectionsStep()}
          {currentStep === 'customize' && renderCustomizeStep()}
          {currentStep === 'preview' && renderPreviewStep()}
        </div>

        {/* Template Viewer Modal */}
        <TemplateViewer
          templateId={previewTemplateId}
          isOpen={showPreviewModal}
          onClose={() => setShowPreviewModal(false)}
          onUseTemplate={(templateId) => {
            setSelectedTemplateForBase(templateId);
            // Find the selected template and set it as base
            const selectedTemplate = availableTemplates.find(t => t.id.toString() === templateId);
            if (selectedTemplate) {
              setBaseTemplate(selectedTemplate);
              setTemplateName(`Custom ${selectedTemplate.name}`);
              setTemplateDescription(`Customized version of ${selectedTemplate.name}`);
            }
            setShowPreviewModal(false);
            setCurrentStep('basic');
          }}
          mode="preview"
        />
      </div>
        </div>
      </div>
    </div>
  );
};

export default TemplateCreationPage;
