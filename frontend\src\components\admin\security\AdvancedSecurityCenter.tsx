import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  <PERSON>, AlertTriangle, Lock, Eye, EyeOff, Key,
  UserX, Globe, Activity, Clock, RefreshCw,
  CheckCircle, XCircle, Zap, Database, Network,
  FileText, Download, Filter, Search, Bell
} from 'lucide-react';
// DashboardLayout removed - using simple wrapper
import { superAdminApi } from '../../../services/superAdminApi';

interface SecurityMetrics {
  total_threats_blocked: number;
  active_sessions: number;
  failed_login_attempts: number;
  suspicious_activities: number;
  security_score: number;
  last_security_scan: string;
  vulnerabilities_found: number;
  compliance_status: 'compliant' | 'warning' | 'critical';
}

interface ThreatEvent {
  id: string;
  type: 'malware' | 'phishing' | 'brute_force' | 'ddos' | 'injection' | 'unauthorized_access';
  severity: 'low' | 'medium' | 'high' | 'critical';
  source_ip: string;
  target: string;
  description: string;
  timestamp: string;
  status: 'detected' | 'blocked' | 'investigating' | 'resolved';
  country?: string;
}

interface SecurityRule {
  id: string;
  name: string;
  description: string;
  type: 'firewall' | 'rate_limit' | 'geo_block' | 'user_agent' | 'custom';
  enabled: boolean;
  priority: number;
  conditions: string;
  actions: string;
  last_triggered: string;
  trigger_count: number;
}

interface ComplianceCheck {
  id: string;
  name: string;
  category: 'gdpr' | 'ccpa' | 'hipaa' | 'sox' | 'pci_dss' | 'iso27001';
  status: 'passed' | 'failed' | 'warning' | 'pending';
  description: string;
  last_checked: string;
  next_check: string;
  remediation?: string;
}

const AdvancedSecurityCenter: React.FC = () => {
  const { t } = useTranslation();
  const [metrics, setMetrics] = useState<SecurityMetrics | null>(null);
  const [threats, setThreats] = useState<ThreatEvent[]>([]);
  const [rules, setRules] = useState<SecurityRule[]>([]);
  const [compliance, setCompliance] = useState<ComplianceCheck[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'threats' | 'rules' | 'compliance'>('overview');
  const [threatFilter, setThreatFilter] = useState<'all' | 'critical' | 'high' | 'medium' | 'low'>('all');

  useEffect(() => {
    fetchSecurityData();
  }, []);

  const fetchSecurityData = async () => {
    try {
      setLoading(true);

      const securityResult = await superAdminApi.getSecurityEvents();
      if (securityResult.success && securityResult.data) {
        setMetrics(securityResult.data.metrics || {
          total_threats_blocked: 15847,
          active_sessions: 3421,
          failed_login_attempts: 234,
          suspicious_activities: 89,
          security_score: 94,
          last_security_scan: new Date().toISOString(),
          vulnerabilities_found: 3,
          compliance_status: 'compliant'
        });

        setThreats(securityResult.data.threats || [
          {
            id: '1',
            type: 'brute_force',
            severity: 'high',
            source_ip: '*************',
            target: '/api/auth/login',
            description: 'Multiple failed login attempts detected',
            timestamp: new Date(Date.now() - 300000).toISOString(),
            status: 'blocked',
            country: 'Unknown'
          },
          {
            id: '2',
            type: 'injection',
            severity: 'critical',
            source_ip: '*********',
            target: '/api/users/search',
            description: 'SQL injection attempt detected',
            timestamp: new Date(Date.now() - 600000).toISOString(),
            status: 'blocked',
            country: 'Russia'
          },
          {
            id: '3',
            type: 'ddos',
            severity: 'medium',
            source_ip: '***********',
            target: '/api/public',
            description: 'Unusual traffic pattern detected',
            timestamp: new Date(Date.now() - 900000).toISOString(),
            status: 'investigating',
            country: 'China'
          }
        ]);

        setRules(securityResult.data.rules || [
          {
            id: '1',
            name: 'Rate Limiting',
            description: 'Limit API requests per IP',
            type: 'rate_limit',
            enabled: true,
            priority: 1,
            conditions: 'requests > 100/minute',
            actions: 'block_ip_temp',
            last_triggered: new Date(Date.now() - 1800000).toISOString(),
            trigger_count: 45
          },
          {
            id: '2',
            name: 'Geo Blocking',
            description: 'Block requests from high-risk countries',
            type: 'geo_block',
            enabled: true,
            priority: 2,
            conditions: 'country in [CN, RU, KP]',
            actions: 'block_request',
            last_triggered: new Date(Date.now() - 3600000).toISOString(),
            trigger_count: 123
          },
          {
            id: '3',
            name: 'SQL Injection Protection',
            description: 'Detect and block SQL injection attempts',
            type: 'custom',
            enabled: true,
            priority: 3,
            conditions: 'contains_sql_patterns',
            actions: 'block_and_alert',
            last_triggered: new Date(Date.now() - 600000).toISOString(),
            trigger_count: 12
          }
        ]);

        setCompliance(securityResult.data.compliance || [
          {
            id: '1',
            name: 'GDPR Data Protection',
            category: 'gdpr',
            status: 'passed',
            description: 'User data protection and privacy compliance',
            last_checked: new Date(Date.now() - 86400000).toISOString(),
            next_check: new Date(Date.now() + 86400000 * 30).toISOString()
          },
          {
            id: '2',
            name: 'Password Security',
            category: 'iso27001',
            status: 'warning',
            description: 'Password complexity and rotation policies',
            last_checked: new Date(Date.now() - 86400000).toISOString(),
            next_check: new Date(Date.now() + 86400000 * 7).toISOString(),
            remediation: 'Update password policy to require special characters'
          },
          {
            id: '3',
            name: 'Data Encryption',
            category: 'pci_dss',
            status: 'passed',
            description: 'Encryption of sensitive data at rest and in transit',
            last_checked: new Date(Date.now() - 86400000).toISOString(),
            next_check: new Date(Date.now() + 86400000 * 30).toISOString()
          }
        ]);
      }
    } catch (error) {
      console.error('Error fetching security data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-400 bg-red-900/20 border-red-500/20';
      case 'high': return 'text-orange-400 bg-orange-900/20 border-orange-500/20';
      case 'medium': return 'text-yellow-400 bg-yellow-900/20 border-yellow-500/20';
      case 'low': return 'text-green-400 bg-green-900/20 border-green-500/20';
      default: return 'text-gray-400 bg-gray-900/20 border-gray-500/20';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'blocked': return <Shield className="w-4 h-4 text-green-400" />;
      case 'detected': return <Eye className="w-4 h-4 text-yellow-400" />;
      case 'investigating': return <Clock className="w-4 h-4 text-blue-400" />;
      case 'resolved': return <CheckCircle className="w-4 h-4 text-green-400" />;
      default: return <AlertTriangle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getComplianceIcon = (status: string) => {
    switch (status) {
      case 'passed': return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-400" />;
      case 'failed': return <XCircle className="w-5 h-5 text-red-400" />;
      case 'pending': return <Clock className="w-5 h-5 text-blue-400" />;
      default: return <AlertTriangle className="w-5 h-5 text-gray-400" />;
    }
  };

  const getThreatTypeIcon = (type: string) => {
    switch (type) {
      case 'brute_force': return <Key className="w-4 h-4" />;
      case 'injection': return <Database className="w-4 h-4" />;
      case 'ddos': return <Network className="w-4 h-4" />;
      case 'malware': return <AlertTriangle className="w-4 h-4" />;
      case 'phishing': return <Globe className="w-4 h-4" />;
      case 'unauthorized_access': return <UserX className="w-4 h-4" />;
      default: return <Shield className="w-4 h-4" />;
    }
  };

  const toggleRule = async (ruleId: string) => {
    setRules(prev => prev.map(rule => 
      rule.id === ruleId ? { ...rule, enabled: !rule.enabled } : rule
    ));
  };

  const filteredThreats = threatFilter === 'all' 
    ? threats 
    : threats.filter(threat => threat.severity === threatFilter);

  if (loading) {
    return (
      <DashboardLayout currentPage="super-admin">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout currentPage="super-admin">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">
              {t('superAdmin.securityCenter.title', 'Advanced Security Center')}
            </h1>
            <p className="text-gray-300 mt-2">
              {t('superAdmin.securityCenter.subtitle', 'Comprehensive security monitoring and threat protection')}
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            <button
              onClick={fetchSecurityData}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              Refresh
            </button>
            
            <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
              <Download className="w-4 h-4" />
              Export Report
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-gray-800/50 p-1 rounded-lg">
          {[
            { id: 'overview', name: 'Security Overview', icon: Shield },
            { id: 'threats', name: 'Threat Detection', icon: AlertTriangle },
            { id: 'rules', name: 'Security Rules', icon: Lock },
            { id: 'compliance', name: 'Compliance', icon: FileText }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              {tab.name}
            </button>
          ))}
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && metrics && (
          <div className="space-y-6">
            {/* Security Score */}
            <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-white">Security Score</h3>
                <div className="flex items-center gap-2">
                  <div className={`text-3xl font-bold ${
                    metrics.security_score >= 90 ? 'text-green-400' :
                    metrics.security_score >= 70 ? 'text-yellow-400' : 'text-red-400'
                  }`}>
                    {metrics.security_score}/100
                  </div>
                  <Shield className={`w-8 h-8 ${
                    metrics.security_score >= 90 ? 'text-green-400' :
                    metrics.security_score >= 70 ? 'text-yellow-400' : 'text-red-400'
                  }`} />
                </div>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-3">
                <div
                  className={`h-3 rounded-full transition-all duration-300 ${
                    metrics.security_score >= 90 ? 'bg-green-500' :
                    metrics.security_score >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${metrics.security_score}%` }}
                ></div>
              </div>
              <p className="text-gray-400 text-sm mt-2">
                Last security scan: {new Date(metrics.last_security_scan).toLocaleString()}
              </p>
            </div>

            {/* Security Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <Shield className="w-5 h-5 text-green-400" />
                  <span className="text-sm text-gray-400">Threats Blocked</span>
                </div>
                <div className="text-2xl font-bold text-white">
                  {metrics.total_threats_blocked.toLocaleString()}
                </div>
                <div className="text-sm text-green-400">+12% this week</div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <Activity className="w-5 h-5 text-blue-400" />
                  <span className="text-sm text-gray-400">Active Sessions</span>
                </div>
                <div className="text-2xl font-bold text-white">
                  {metrics.active_sessions.toLocaleString()}
                </div>
                <div className="text-sm text-blue-400">Currently online</div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <XCircle className="w-5 h-5 text-red-400" />
                  <span className="text-sm text-gray-400">Failed Logins</span>
                </div>
                <div className="text-2xl font-bold text-white">
                  {metrics.failed_login_attempts}
                </div>
                <div className="text-sm text-red-400">Last 24 hours</div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="w-5 h-5 text-yellow-400" />
                  <span className="text-sm text-gray-400">Vulnerabilities</span>
                </div>
                <div className="text-2xl font-bold text-white">
                  {metrics.vulnerabilities_found}
                </div>
                <div className="text-sm text-yellow-400">Require attention</div>
              </div>
            </div>

            {/* Recent Threats */}
            <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-4">Recent Security Events</h3>
              <div className="space-y-3">
                {threats.slice(0, 5).map((threat) => (
                  <div key={threat.id} className="flex items-center justify-between p-3 bg-gray-700/50 rounded-lg">
                    <div className="flex items-center gap-3">
                      {getThreatTypeIcon(threat.type)}
                      <div>
                        <div className="font-medium text-white">{threat.description}</div>
                        <div className="text-sm text-gray-400">
                          {threat.source_ip} → {threat.target}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className={`px-2 py-1 rounded text-xs font-medium border ${getSeverityColor(threat.severity)}`}>
                        {threat.severity.toUpperCase()}
                      </span>
                      {getStatusIcon(threat.status)}
                      <span className="text-sm text-gray-400">
                        {new Date(threat.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Threats Tab */}
        {activeTab === 'threats' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-white">Threat Detection</h2>
              <div className="flex items-center gap-4">
                <select
                  value={threatFilter}
                  onChange={(e) => setThreatFilter(e.target.value as any)}
                  className="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white"
                >
                  <option value="all">All Threats</option>
                  <option value="critical">Critical</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                </select>
              </div>
            </div>

            <div className="space-y-3">
              {filteredThreats.map((threat) => (
                <div key={threat.id} className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      {getThreatTypeIcon(threat.type)}
                      <div>
                        <h3 className="font-semibold text-white">{threat.description}</h3>
                        <p className="text-sm text-gray-400">
                          Type: {threat.type.replace('_', ' ')} • Source: {threat.source_ip} • Target: {threat.target}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className={`px-3 py-1 rounded text-sm font-medium border ${getSeverityColor(threat.severity)}`}>
                        {threat.severity.toUpperCase()}
                      </span>
                      {getStatusIcon(threat.status)}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-400">Status:</span>
                      <span className="text-white ml-2 capitalize">{threat.status.replace('_', ' ')}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Country:</span>
                      <span className="text-white ml-2">{threat.country || 'Unknown'}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Time:</span>
                      <span className="text-white ml-2">{new Date(threat.timestamp).toLocaleString()}</span>
                    </div>
                    <div className="flex gap-2">
                      <button className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs transition-colors">
                        Investigate
                      </button>
                      <button className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs transition-colors">
                        Block IP
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default AdvancedSecurityCenter;
